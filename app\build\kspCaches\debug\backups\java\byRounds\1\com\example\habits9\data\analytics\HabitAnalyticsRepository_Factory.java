package com.example.habits9.data.analytics;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class HabitAnalyticsRepository_Factory implements Factory<HabitAnalyticsRepository> {
  private final Provider<HabitAnalyticsUseCase> analyticsUseCaseProvider;

  public HabitAnalyticsRepository_Factory(
      Provider<HabitAnalyticsUseCase> analyticsUseCaseProvider) {
    this.analyticsUseCaseProvider = analyticsUseCaseProvider;
  }

  @Override
  public HabitAnalyticsRepository get() {
    return newInstance(analyticsUseCaseProvider.get());
  }

  public static HabitAnalyticsRepository_Factory create(
      Provider<HabitAnalyticsUseCase> analyticsUseCaseProvider) {
    return new HabitAnalyticsRepository_Factory(analyticsUseCaseProvider);
  }

  public static HabitAnalyticsRepository newInstance(HabitAnalyticsUseCase analyticsUseCase) {
    return new HabitAnalyticsRepository(analyticsUseCase);
  }
}
