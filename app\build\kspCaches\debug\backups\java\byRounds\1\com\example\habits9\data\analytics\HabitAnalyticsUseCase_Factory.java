package com.example.habits9.data.analytics;

import com.example.habits9.data.CompletionRepository;
import com.example.habits9.data.HabitRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class HabitAnalyticsUseCase_Factory implements Factory<HabitAnalyticsUseCase> {
  private final Provider<HabitRepository> habitRepositoryProvider;

  private final Provider<CompletionRepository> completionRepositoryProvider;

  public HabitAnalyticsUseCase_Factory(Provider<HabitRepository> habitRepositoryProvider,
      Provider<CompletionRepository> completionRepositoryProvider) {
    this.habitRepositoryProvider = habitRepositoryProvider;
    this.completionRepositoryProvider = completionRepositoryProvider;
  }

  @Override
  public HabitAnalyticsUseCase get() {
    return newInstance(habitRepositoryProvider.get(), completionRepositoryProvider.get());
  }

  public static HabitAnalyticsUseCase_Factory create(
      Provider<HabitRepository> habitRepositoryProvider,
      Provider<CompletionRepository> completionRepositoryProvider) {
    return new HabitAnalyticsUseCase_Factory(habitRepositoryProvider, completionRepositoryProvider);
  }

  public static HabitAnalyticsUseCase newInstance(HabitRepository habitRepository,
      CompletionRepository completionRepository) {
    return new HabitAnalyticsUseCase(habitRepository, completionRepository);
  }
}
