# Prompt 5.8: Implement New Habit Score Calculation Logic

## A. The Objective & Context

The goal of this task is to implement a new, more sophisticated **habit score calculation engine**. This is a foundational logic task that will power all future analytics UI. The new system uses a weighted average for the overall score and provides specific calculations for different timeframes, correctly handling both "Yes/No" and "Measurable" habits.

**CRITICAL GUIDANCE**: This new scoring system is the **single source of truth** for all performance metrics going forward.
1.  Your first step must be to investigate the codebase for any pre-existing habit score or streak calculation logic.
2.  Any old or different scoring systems **must be completely removed and replaced** with this new implementation. This is not an addition; it is a replacement to ensure consistency across the entire application.

## B. Detailed Implementation Plan

The implementation should be divided into two main parts: the Overall Score and the Timeframe Scores.

### 1. Implement the Overall Habit Score
- This score is a weighted average that reflects both recent momentum and long-term consistency.
- Create a function `getOverallScore(habitId)` that implements the following formula:
  **`Overall Score = (Score_Last30Days * 0.6) + (Score_AllTime * 0.4)`**
- `Score_Last30Days`: The habit's score calculated over the last 30 days.
- `Score_AllTime`: The habit's score calculated over its entire history.
- You will need to implement the functions to calculate these component scores first (see Part 2).

### 2. Implement Timeframe Score Calculations
- Create a primary function `getScoreForPeriod(habitId, timePeriod)` where `timePeriod` can be Day, Week, Month, Quarter, or Year.
- The fundamental formula for any period is:
  **`Score = (Sum of Daily Scores in Period / Number of Scheduled Days in Period) * 100`**
- The logic must correctly calculate the "Daily Score" based on the habit type.

#### 2.1. Daily Score for "Yes/No" Habits
- If a habit is scheduled and **completed**, its Daily Score is **1**.
- If a habit is scheduled and **missed**, its Daily Score is **0**.

#### 2.2. Daily Score for "Measurable" Habits
- The Daily Score is the ratio of the logged amount to the goal amount.
- **`Daily Score = (Amount Logged / Goal Amount)`**
- **CRITICAL**: This score must be **capped at 1**. A user cannot get "extra credit" for exceeding their daily goal in the context of the score calculation. For example, if the goal is "run 5 km" and the user runs 7 km, the Daily Score for that day is 1, not 1.4.

## C. Meticulous Verification Plan

### 1. "Yes/No" Habit Verification
- Create a "Yes/No" habit scheduled for every day.
- For the last 30 days, complete it 15 times. **Verify `getScoreForPeriod` for the Month returns 50.**
- For the last 7 days, complete it 7 times. **Verify `getScoreForPeriod` for the Week returns 100.**

### 2. "Measurable" Habit Verification
- Create a "Measurable" habit with a goal of "Read 20 pages".
- On Day 1, log 10 pages (Daily Score = 0.5).
- On Day 2, log 20 pages (Daily Score = 1.0).
- On Day 3, log 30 pages (Daily Score = 1.0, because it's capped).
- On Day 4, it was scheduled, but you logged 0 pages (Daily Score = 0).
- **CRITICAL**: Call `getScoreForPeriod` for these 4 days. The total score is (0.5 + 1.0 + 1.0 + 0) = 2.5. The number of scheduled days is 4. The result should be **(2.5 / 4) * 100 = 62.5**.

### 3. Overall Weighted Score Verification
- Create a new habit and complete it perfectly for 30 days.
- Its `Score_Last30Days` is 100. Its `Score_AllTime` is 100.
- **Verify `getOverallScore` returns 100.** `(100 * 0.6) + (100 * 0.4) = 100`.
- Now, simulate the next 30 days with a score of only 50%. The new `Score_Last30Days` is 50. The `Score_AllTime` is now 75 (average of the first 30 good days and 30 bad days).
- **CRITICAL**: Verify the new `getOverallScore` returns **60**. `(50 * 0.6) + (75 * 0.4) = 30 + 30 = 60`.

## D. Mandatory Development Guidelines

These practices must be followed during all phases of development—planning, implementation, and review.

### 1. Refer to the Style Guide
Before starting any feature:
- Always consult the **style guide** for rules related to UI/UX, layout, naming conventions, spacing, colors, and design patterns.
- The style guide is located in the **`style.md` file in the root folder`**.
- It is the **single source of truth** for styling decisions.

### 2. Study the Reference Project
Prior to implementation:
- Review the **reference project** located in the `uhabits-dev` folder (in the root directory).
- Understand how similar features have been approached to maintain consistency and avoid duplications or contradictions.
- The reference project serves as a **blueprint** for implementation.
- This step is mandatory. **Do not proceed to implementation without this step.**

### 3. Understand the Existing Project Structure
Before writing any code:
- Spend time exploring and understanding how the current system is structured.
- Even for new features, existing components or utility functions may be reusable.
- Integrate changes **cleanly into the existing architecture** instead of creating disconnected code.

### 4. Maintain a Clean Codebase
After implementing features:
- Remove any temporary, test, or duplicate files, folders, routes, or unused components that were created during development.
- Keep the codebase **organized and clutter-free**.

### 5. Pause If There Is Any Confusion
If at any point the requirements are unclear:
- **Do not proceed** based on assumptions.
- Immediately pause and seek clarification either from the project lead or directly from me.
- It is better to get clarity than to redo or fix avoidable mistakes later.

### 6. Remove Unused Old Implementations
As part of final review:
- Identify and delete **any old, unused code** that was implemented earlier but is no longer in use.
- This includes obsolete routes, modules, features, or legacy logic.