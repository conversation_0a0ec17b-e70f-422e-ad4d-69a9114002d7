package com.example.habits9.ui.habitreorder;

import com.example.habits9.data.HabitRepository;
import com.example.habits9.data.UserPreferencesRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class HabitReorderViewModel_Factory implements Factory<HabitReorderViewModel> {
  private final Provider<HabitRepository> habitRepositoryProvider;

  private final Provider<UserPreferencesRepository> userPreferencesRepositoryProvider;

  public HabitReorderViewModel_Factory(Provider<HabitRepository> habitRepositoryProvider,
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider) {
    this.habitRepositoryProvider = habitRepositoryProvider;
    this.userPreferencesRepositoryProvider = userPreferencesRepositoryProvider;
  }

  @Override
  public HabitReorderViewModel get() {
    return newInstance(habitRepositoryProvider.get(), userPreferencesRepositoryProvider.get());
  }

  public static HabitReorderViewModel_Factory create(
      Provider<HabitRepository> habitRepositoryProvider,
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider) {
    return new HabitReorderViewModel_Factory(habitRepositoryProvider, userPreferencesRepositoryProvider);
  }

  public static HabitReorderViewModel newInstance(HabitRepository habitRepository,
      UserPreferencesRepository userPreferencesRepository) {
    return new HabitReorderViewModel(habitRepository, userPreferencesRepository);
  }
}
