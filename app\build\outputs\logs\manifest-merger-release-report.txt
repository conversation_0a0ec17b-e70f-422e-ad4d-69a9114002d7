-- Merging decision tree log ---
manifest
ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:2:1-28:12
INJECTED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:2:1-28:12
INJECTED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:2:1-28:12
INJECTED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:2:1-28:12
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd5d47b5cb134ef8835db2319867c727\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a68a0f572e4254addb1cce13d80a6bc4\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\9961d480dd52b08a59f7662c2ca5928d\transformed\navigation-common-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\f5e74ae7235dc3aa9a635dc7def8a927\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\94362aa43689fc346a39521984e13d85\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\f673c743eafd791dd509a8c731e78ee9\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\831c276a7e76d206619cfbeff72b0a92\transformed\navigation-compose-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.dagger:hilt-android:2.49] C:\Users\<USER>\.gradle\caches\8.13\transforms\63744a0504942ef609f7a999ccc09125\transformed\hilt-android-2.49\AndroidManifest.xml:16:1-19:12
MERGED from [com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1b164dcef77965ed0bfc5203894ab9f\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:2:1-17:12
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:17:1-75:12
MERGED from [com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\196d76f4a5dd55b436535b048bf59be0\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847f490becd90c69cf2a6765d8a1684e\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3db6eba360572034a2f2f88cf5cbe97f\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:15:1-25:12
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ee68198d373217854dc4bfeae9e281c\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9549621c83de7ce5d44746d266914d0\transformed\credentials-1.2.0-rc01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:17:1-44:12
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\91d8c2af4aeecc49d2e04065953e388c\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\45a2caabac87784ffd78bee994918aab\transformed\play-services-fido-20.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\cadd01bdf4ea87d064b20177f562f98d\transformed\play-services-auth-base-18.0.4\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6806dc609573cce0b061254c5042724b\transformed\play-services-base-18.0.1\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\974ec625152fe404856b82ebcfbe2275\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9f2b57f3ef60002ed6afd78a526cfd3\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35088881d9fa2f6252e2bf289aa77253\transformed\recaptcha-18.5.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5b0fdf0448035a5e9f8fc9afbba87cfe\transformed\integrity-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\07f408d290f2498181cf432fde42d716\transformed\browser-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c642d4d4375a965876dfe78fd94be9a2\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\008592940c9e6222f40c67ebbdc15e22\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8aaeb18dd8abb7cc16bdd65ee58c7a94\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bccfad6ae2f02e3c57fa854656ed90df\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\0811a5ad27612b37de39e72b0b950217\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\edebfaddc53a2536544589ace67201cb\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\838c3fbe604a4f612d98c5c8ed065549\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\40f6928b2ad33db7b5236af4f56ffa9b\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\364994c224617bb5658cf91cfb556286\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\6099cc662b3d3d21922c8764e347ae3a\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\0c3ce5b45407ebaf2d3c7543a4590c1c\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\13d4227fbf750a42b4666692ab5cd43f\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ea04e12a8b69c74e22a08f2969fa89f\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\597a02393a15f5e1cca4bc44815f5d4b\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba89c1e9bda300913f5635e34e46d318\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6ef72d2134d492a4ad92cbd50b61265\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ffa651d7a27ee3650c5777c099d70df\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\eea6fe58f93d024edefa4431b0bf04b3\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa95668a250866bf56b6d7fcecf67bbf\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5398f023e9c66f7188e5eab45c6b986b\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e82ebc2c581c87be245ec8195e736c6\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8240e59832797f966f8357204103d185\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0673b1aecd457c10221017e11ef110b\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dc64634b106adb8838b7a58d4a92456f\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efd27e88bfa33be47f586616d7b4ba83\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5cc54e9ab4447dadf937d51d8741e594\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c12a3b1e67118b640fe2a9b888deadcb\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a29f5253caa2646938854d3d943fe6b6\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\92faffc5ee3d50988e6ae9072fb227ba\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f1d43690ddcf9f6008451e8e8f9667a\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bacfd3455d7e4a9afe5c8f67966528e5\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\22d7b470f5315ff3c587340687d4f1dd\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0062a5e1d214efb62fafece10c34c46\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd4c28a9cd816fee213d1e3481c2e9cc\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6a65e1c87007daa216d632c03dac5af\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b35fb13ed5836ad571fb8021dd09a89\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\046afb671d1e977233d2eec488975af4\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1aa9a432c263f8a100b3f9a7aa5543c\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-core-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\402b5e3804129992480eca3659c7d584\transformed\datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5584aa465d60e662f379914378d30223\transformed\datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\07c974fda8dda040bf5d85ce37f1fff0\transformed\datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d210f13c8a5f946ea8d81ec9691ec876\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4106571b134d49d860c08c6ca142a08e\transformed\play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b50d9d4c3fe397e48544049185057d4\transformed\fragment-1.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a56dc59a73c285a5fb1ee061c400e737\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\48e1bc35714d96a58d86e363a2cb1599\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6178303f9a772de12d58e04801175a3e\transformed\activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6dd6510939024db6dfff1d92597524bb\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea9d059a86207d33d82d4231a9c25787\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0070702b2964f4d8071c3fba3be3487a\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b6e88755ad5b4c533fcd6b2fa8fb2e3\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f427624148d6645f2969848fa880372b\transformed\googleid-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd0a24e78751fed9af27fb4e53c48126\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d8f0309e03a4b9f837e1454dfa57629\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e71d6d93707fe0d9686d0f9b979770d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\be70b5c5a261adfd3c0eef6a0633a3b0\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\583e583feed48ae26410889a23238726\transformed\firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34cf54f4e822c3c12f6e2530da4cd157\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\666aa4272157ad17e3403a84df4bafb8\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fcd4f448843b14062b4added1dcd207a\transformed\grpc-android-1.62.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.dagger:dagger-lint-aar:2.49] C:\Users\<USER>\.gradle\caches\8.13\transforms\6253c483c38d3105b9d9fd2d7fd74a04\transformed\dagger-lint-aar-2.49\AndroidManifest.xml:16:1-19:12
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\332e952a1d9fef5d4da2897fbe118cb6\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\512de977da2a1ddbca08eb2f7061b9db\transformed\core-common-2.0.3\AndroidManifest.xml:2:1-21:12
	package
		INJECTED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:2:11-69
application
ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:5:5-26:19
INJECTED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:5:5-26:19
MERGED from [com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1b164dcef77965ed0bfc5203894ab9f\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1b164dcef77965ed0bfc5203894ab9f\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\196d76f4a5dd55b436535b048bf59be0\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\196d76f4a5dd55b436535b048bf59be0\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847f490becd90c69cf2a6765d8a1684e\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847f490becd90c69cf2a6765d8a1684e\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3db6eba360572034a2f2f88cf5cbe97f\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3db6eba360572034a2f2f88cf5cbe97f\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\45a2caabac87784ffd78bee994918aab\transformed\play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\45a2caabac87784ffd78bee994918aab\transformed\play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6806dc609573cce0b061254c5042724b\transformed\play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6806dc609573cce0b061254c5042724b\transformed\play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\974ec625152fe404856b82ebcfbe2275\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\974ec625152fe404856b82ebcfbe2275\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9f2b57f3ef60002ed6afd78a526cfd3\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9f2b57f3ef60002ed6afd78a526cfd3\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5b0fdf0448035a5e9f8fc9afbba87cfe\transformed\integrity-1.3.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5b0fdf0448035a5e9f8fc9afbba87cfe\transformed\integrity-1.3.0\AndroidManifest.xml:5:5-6:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba89c1e9bda300913f5635e34e46d318\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba89c1e9bda300913f5635e34e46d318\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6ef72d2134d492a4ad92cbd50b61265\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6ef72d2134d492a4ad92cbd50b61265\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0062a5e1d214efb62fafece10c34c46\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0062a5e1d214efb62fafece10c34c46\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d210f13c8a5f946ea8d81ec9691ec876\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d210f13c8a5f946ea8d81ec9691ec876\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4106571b134d49d860c08c6ca142a08e\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4106571b134d49d860c08c6ca142a08e\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd0a24e78751fed9af27fb4e53c48126\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd0a24e78751fed9af27fb4e53c48126\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e71d6d93707fe0d9686d0f9b979770d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e71d6d93707fe0d9686d0f9b979770d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\512de977da2a1ddbca08eb2f7061b9db\transformed\core-common-2.0.3\AndroidManifest.xml:11:5-19:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\512de977da2a1ddbca08eb2f7061b9db\transformed\core-common-2.0.3\AndroidManifest.xml:11:5-19:19
	android:extractNativeLibs
		INJECTED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0062a5e1d214efb62fafece10c34c46\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:13:9-35
	android:label
		ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:11:9-41
	android:fullBackupContent
		ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:9:9-54
	android:roundIcon
		ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:12:9-54
	android:icon
		ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:10:9-43
	android:allowBackup
		ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:7:9-35
	android:theme
		ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:14:9-48
	android:dataExtractionRules
		ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:8:9-65
	android:name
		ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:6:9-61
activity#com.example.uhabits_99.MainActivity
ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:15:9-25:20
	android:label
		ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:18:13-45
	android:exported
		ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:17:13-36
	android:theme
		ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:19:13-52
	android:name
		ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:16:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:20:13-24:29
action#android.intent.action.MAIN
ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:21:17-69
	android:name
		ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:21:25-66
category#android.intent.category.LAUNCHER
ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:23:17-77
	android:name
		ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:23:27-74
uses-sdk
INJECTED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml
INJECTED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd5d47b5cb134ef8835db2319867c727\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd5d47b5cb134ef8835db2319867c727\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a68a0f572e4254addb1cce13d80a6bc4\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a68a0f572e4254addb1cce13d80a6bc4\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\9961d480dd52b08a59f7662c2ca5928d\transformed\navigation-common-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\9961d480dd52b08a59f7662c2ca5928d\transformed\navigation-common-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\f5e74ae7235dc3aa9a635dc7def8a927\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\f5e74ae7235dc3aa9a635dc7def8a927\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\94362aa43689fc346a39521984e13d85\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\94362aa43689fc346a39521984e13d85\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\f673c743eafd791dd509a8c731e78ee9\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\f673c743eafd791dd509a8c731e78ee9\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\831c276a7e76d206619cfbeff72b0a92\transformed\navigation-compose-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\831c276a7e76d206619cfbeff72b0a92\transformed\navigation-compose-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [com.google.dagger:hilt-android:2.49] C:\Users\<USER>\.gradle\caches\8.13\transforms\63744a0504942ef609f7a999ccc09125\transformed\hilt-android-2.49\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.49] C:\Users\<USER>\.gradle\caches\8.13\transforms\63744a0504942ef609f7a999ccc09125\transformed\hilt-android-2.49\AndroidManifest.xml:18:3-42
MERGED from [com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1b164dcef77965ed0bfc5203894ab9f\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1b164dcef77965ed0bfc5203894ab9f\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:21:5-23:151
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:21:5-23:151
MERGED from [com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\196d76f4a5dd55b436535b048bf59be0\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\196d76f4a5dd55b436535b048bf59be0\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847f490becd90c69cf2a6765d8a1684e\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847f490becd90c69cf2a6765d8a1684e\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3db6eba360572034a2f2f88cf5cbe97f\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3db6eba360572034a2f2f88cf5cbe97f\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ee68198d373217854dc4bfeae9e281c\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ee68198d373217854dc4bfeae9e281c\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9549621c83de7ce5d44746d266914d0\transformed\credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9549621c83de7ce5d44746d266914d0\transformed\credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\91d8c2af4aeecc49d2e04065953e388c\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\91d8c2af4aeecc49d2e04065953e388c\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\45a2caabac87784ffd78bee994918aab\transformed\play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\45a2caabac87784ffd78bee994918aab\transformed\play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\cadd01bdf4ea87d064b20177f562f98d\transformed\play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\cadd01bdf4ea87d064b20177f562f98d\transformed\play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6806dc609573cce0b061254c5042724b\transformed\play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6806dc609573cce0b061254c5042724b\transformed\play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\974ec625152fe404856b82ebcfbe2275\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\974ec625152fe404856b82ebcfbe2275\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9f2b57f3ef60002ed6afd78a526cfd3\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9f2b57f3ef60002ed6afd78a526cfd3\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35088881d9fa2f6252e2bf289aa77253\transformed\recaptcha-18.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35088881d9fa2f6252e2bf289aa77253\transformed\recaptcha-18.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5b0fdf0448035a5e9f8fc9afbba87cfe\transformed\integrity-1.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5b0fdf0448035a5e9f8fc9afbba87cfe\transformed\integrity-1.3.0\AndroidManifest.xml:4:5-44
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\07f408d290f2498181cf432fde42d716\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\07f408d290f2498181cf432fde42d716\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c642d4d4375a965876dfe78fd94be9a2\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c642d4d4375a965876dfe78fd94be9a2\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\008592940c9e6222f40c67ebbdc15e22\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\008592940c9e6222f40c67ebbdc15e22\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8aaeb18dd8abb7cc16bdd65ee58c7a94\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8aaeb18dd8abb7cc16bdd65ee58c7a94\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bccfad6ae2f02e3c57fa854656ed90df\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bccfad6ae2f02e3c57fa854656ed90df\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\0811a5ad27612b37de39e72b0b950217\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\0811a5ad27612b37de39e72b0b950217\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\edebfaddc53a2536544589ace67201cb\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\edebfaddc53a2536544589ace67201cb\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\838c3fbe604a4f612d98c5c8ed065549\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\838c3fbe604a4f612d98c5c8ed065549\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\40f6928b2ad33db7b5236af4f56ffa9b\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\40f6928b2ad33db7b5236af4f56ffa9b\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\364994c224617bb5658cf91cfb556286\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\364994c224617bb5658cf91cfb556286\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\6099cc662b3d3d21922c8764e347ae3a\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\6099cc662b3d3d21922c8764e347ae3a\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\0c3ce5b45407ebaf2d3c7543a4590c1c\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\0c3ce5b45407ebaf2d3c7543a4590c1c\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\13d4227fbf750a42b4666692ab5cd43f\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\13d4227fbf750a42b4666692ab5cd43f\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ea04e12a8b69c74e22a08f2969fa89f\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ea04e12a8b69c74e22a08f2969fa89f\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\597a02393a15f5e1cca4bc44815f5d4b\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\597a02393a15f5e1cca4bc44815f5d4b\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba89c1e9bda300913f5635e34e46d318\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba89c1e9bda300913f5635e34e46d318\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6ef72d2134d492a4ad92cbd50b61265\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6ef72d2134d492a4ad92cbd50b61265\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ffa651d7a27ee3650c5777c099d70df\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ffa651d7a27ee3650c5777c099d70df\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\eea6fe58f93d024edefa4431b0bf04b3\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\eea6fe58f93d024edefa4431b0bf04b3\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa95668a250866bf56b6d7fcecf67bbf\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa95668a250866bf56b6d7fcecf67bbf\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5398f023e9c66f7188e5eab45c6b986b\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5398f023e9c66f7188e5eab45c6b986b\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e82ebc2c581c87be245ec8195e736c6\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e82ebc2c581c87be245ec8195e736c6\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8240e59832797f966f8357204103d185\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8240e59832797f966f8357204103d185\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0673b1aecd457c10221017e11ef110b\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0673b1aecd457c10221017e11ef110b\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dc64634b106adb8838b7a58d4a92456f\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dc64634b106adb8838b7a58d4a92456f\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efd27e88bfa33be47f586616d7b4ba83\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efd27e88bfa33be47f586616d7b4ba83\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5cc54e9ab4447dadf937d51d8741e594\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5cc54e9ab4447dadf937d51d8741e594\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c12a3b1e67118b640fe2a9b888deadcb\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c12a3b1e67118b640fe2a9b888deadcb\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a29f5253caa2646938854d3d943fe6b6\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a29f5253caa2646938854d3d943fe6b6\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\92faffc5ee3d50988e6ae9072fb227ba\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\92faffc5ee3d50988e6ae9072fb227ba\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f1d43690ddcf9f6008451e8e8f9667a\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f1d43690ddcf9f6008451e8e8f9667a\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bacfd3455d7e4a9afe5c8f67966528e5\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bacfd3455d7e4a9afe5c8f67966528e5\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\22d7b470f5315ff3c587340687d4f1dd\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\22d7b470f5315ff3c587340687d4f1dd\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0062a5e1d214efb62fafece10c34c46\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0062a5e1d214efb62fafece10c34c46\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd4c28a9cd816fee213d1e3481c2e9cc\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd4c28a9cd816fee213d1e3481c2e9cc\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6a65e1c87007daa216d632c03dac5af\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6a65e1c87007daa216d632c03dac5af\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b35fb13ed5836ad571fb8021dd09a89\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b35fb13ed5836ad571fb8021dd09a89\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\046afb671d1e977233d2eec488975af4\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\046afb671d1e977233d2eec488975af4\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1aa9a432c263f8a100b3f9a7aa5543c\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1aa9a432c263f8a100b3f9a7aa5543c\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\402b5e3804129992480eca3659c7d584\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\402b5e3804129992480eca3659c7d584\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5584aa465d60e662f379914378d30223\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5584aa465d60e662f379914378d30223\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\07c974fda8dda040bf5d85ce37f1fff0\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\07c974fda8dda040bf5d85ce37f1fff0\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d210f13c8a5f946ea8d81ec9691ec876\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d210f13c8a5f946ea8d81ec9691ec876\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4106571b134d49d860c08c6ca142a08e\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4106571b134d49d860c08c6ca142a08e\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b50d9d4c3fe397e48544049185057d4\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b50d9d4c3fe397e48544049185057d4\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a56dc59a73c285a5fb1ee061c400e737\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a56dc59a73c285a5fb1ee061c400e737\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\48e1bc35714d96a58d86e363a2cb1599\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\48e1bc35714d96a58d86e363a2cb1599\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6178303f9a772de12d58e04801175a3e\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6178303f9a772de12d58e04801175a3e\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6dd6510939024db6dfff1d92597524bb\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6dd6510939024db6dfff1d92597524bb\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea9d059a86207d33d82d4231a9c25787\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea9d059a86207d33d82d4231a9c25787\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0070702b2964f4d8071c3fba3be3487a\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0070702b2964f4d8071c3fba3be3487a\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b6e88755ad5b4c533fcd6b2fa8fb2e3\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b6e88755ad5b4c533fcd6b2fa8fb2e3\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f427624148d6645f2969848fa880372b\transformed\googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f427624148d6645f2969848fa880372b\transformed\googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd0a24e78751fed9af27fb4e53c48126\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd0a24e78751fed9af27fb4e53c48126\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d8f0309e03a4b9f837e1454dfa57629\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d8f0309e03a4b9f837e1454dfa57629\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e71d6d93707fe0d9686d0f9b979770d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e71d6d93707fe0d9686d0f9b979770d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\be70b5c5a261adfd3c0eef6a0633a3b0\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\be70b5c5a261adfd3c0eef6a0633a3b0\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\583e583feed48ae26410889a23238726\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\583e583feed48ae26410889a23238726\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34cf54f4e822c3c12f6e2530da4cd157\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34cf54f4e822c3c12f6e2530da4cd157\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\666aa4272157ad17e3403a84df4bafb8\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\666aa4272157ad17e3403a84df4bafb8\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fcd4f448843b14062b4added1dcd207a\transformed\grpc-android-1.62.2\AndroidManifest.xml:5:5-44
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fcd4f448843b14062b4added1dcd207a\transformed\grpc-android-1.62.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.dagger:dagger-lint-aar:2.49] C:\Users\<USER>\.gradle\caches\8.13\transforms\6253c483c38d3105b9d9fd2d7fd74a04\transformed\dagger-lint-aar-2.49\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.49] C:\Users\<USER>\.gradle\caches\8.13\transforms\6253c483c38d3105b9d9fd2d7fd74a04\transformed\dagger-lint-aar-2.49\AndroidManifest.xml:18:3-42
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\332e952a1d9fef5d4da2897fbe118cb6\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\332e952a1d9fef5d4da2897fbe118cb6\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\512de977da2a1ddbca08eb2f7061b9db\transformed\core-common-2.0.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\512de977da2a1ddbca08eb2f7061b9db\transformed\core-common-2.0.3\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:23:9-148
	android:targetSdkVersion
		INJECTED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1b164dcef77965ed0bfc5203894ab9f\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\196d76f4a5dd55b436535b048bf59be0\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\196d76f4a5dd55b436535b048bf59be0\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847f490becd90c69cf2a6765d8a1684e\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847f490becd90c69cf2a6765d8a1684e\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\974ec625152fe404856b82ebcfbe2275\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\974ec625152fe404856b82ebcfbe2275\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
	android:exported
		ADDED from [com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1b164dcef77965ed0bfc5203894ab9f\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:10:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1b164dcef77965ed0bfc5203894ab9f\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:9:13-84
meta-data#com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar
ADDED from [com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1b164dcef77965ed0bfc5203894ab9f\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:11:13-13:85
	android:value
		ADDED from [com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1b164dcef77965ed0bfc5203894ab9f\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:13:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1b164dcef77965ed0bfc5203894ab9f\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:12:17-119
uses-permission#android.permission.INTERNET
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847f490becd90c69cf2a6765d8a1684e\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847f490becd90c69cf2a6765d8a1684e\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:11:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35088881d9fa2f6252e2bf289aa77253\transformed\recaptcha-18.5.1\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35088881d9fa2f6252e2bf289aa77253\transformed\recaptcha-18.5.1\AndroidManifest.xml:7:5-67
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:25:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847f490becd90c69cf2a6765d8a1684e\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847f490becd90c69cf2a6765d8a1684e\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:10:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35088881d9fa2f6252e2bf289aa77253\transformed\recaptcha-18.5.1\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35088881d9fa2f6252e2bf289aa77253\transformed\recaptcha-18.5.1\AndroidManifest.xml:8:5-79
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fcd4f448843b14062b4added1dcd207a\transformed\grpc-android-1.62.2\AndroidManifest.xml:7:5-79
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fcd4f448843b14062b4added1dcd207a\transformed\grpc-android-1.62.2\AndroidManifest.xml:7:5-79
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:26:22-76
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:35:13-45:29
action#android.intent.action.VIEW
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:17-69
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:25-66
category#android.intent.category.DEFAULT
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:17-76
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:17-78
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:27-75
data
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
	android:path
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:43:21-37
	android:host
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
	android:scheme
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:53:13-63:29
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:70:17-109
meta-data#com.google.firebase.components:com.google.firebase.firestore.ktx.FirebaseFirestoreLegacyRegistrar
ADDED from [com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\196d76f4a5dd55b436535b048bf59be0\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\196d76f4a5dd55b436535b048bf59be0\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\196d76f4a5dd55b436535b048bf59be0\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:13:17-129
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar
ADDED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847f490becd90c69cf2a6765d8a1684e\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847f490becd90c69cf2a6765d8a1684e\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847f490becd90c69cf2a6765d8a1684e\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:18:17-122
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar
ADDED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847f490becd90c69cf2a6765d8a1684e\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847f490becd90c69cf2a6765d8a1684e\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847f490becd90c69cf2a6765d8a1684e\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:21:17-111
service#androidx.credentials.playservices.CredentialProviderMetadataHolder
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:28:13-60
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
meta-data#androidx.credentials.CREDENTIAL_PROVIDER_KEY
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
	android:value
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
activity#androidx.credentials.playservices.HiddenActivity
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
	android:fitsSystemWindows
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
	android:configChanges
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
	android:theme
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6806dc609573cce0b061254c5042724b\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6806dc609573cce0b061254c5042724b\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6806dc609573cce0b061254c5042724b\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6806dc609573cce0b061254c5042724b\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\974ec625152fe404856b82ebcfbe2275\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\974ec625152fe404856b82ebcfbe2275\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\974ec625152fe404856b82ebcfbe2275\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
uses-permission#com.google.android.providers.gsf.permission.READ_GSERVICES
ADDED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35088881d9fa2f6252e2bf289aa77253\transformed\recaptcha-18.5.1\AndroidManifest.xml:9:5-98
	android:name
		ADDED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35088881d9fa2f6252e2bf289aa77253\transformed\recaptcha-18.5.1\AndroidManifest.xml:9:22-95
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba89c1e9bda300913f5635e34e46d318\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6ef72d2134d492a4ad92cbd50b61265\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6ef72d2134d492a4ad92cbd50b61265\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e71d6d93707fe0d9686d0f9b979770d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e71d6d93707fe0d9686d0f9b979770d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba89c1e9bda300913f5635e34e46d318\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba89c1e9bda300913f5635e34e46d318\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba89c1e9bda300913f5635e34e46d318\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba89c1e9bda300913f5635e34e46d318\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba89c1e9bda300913f5635e34e46d318\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba89c1e9bda300913f5635e34e46d318\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba89c1e9bda300913f5635e34e46d318\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6ef72d2134d492a4ad92cbd50b61265\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6ef72d2134d492a4ad92cbd50b61265\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6ef72d2134d492a4ad92cbd50b61265\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0062a5e1d214efb62fafece10c34c46\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0062a5e1d214efb62fafece10c34c46\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0062a5e1d214efb62fafece10c34c46\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.example.uhabits_99.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0062a5e1d214efb62fafece10c34c46\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0062a5e1d214efb62fafece10c34c46\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0062a5e1d214efb62fafece10c34c46\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0062a5e1d214efb62fafece10c34c46\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0062a5e1d214efb62fafece10c34c46\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.uhabits_99.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0062a5e1d214efb62fafece10c34c46\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0062a5e1d214efb62fafece10c34c46\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4106571b134d49d860c08c6ca142a08e\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4106571b134d49d860c08c6ca142a08e\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4106571b134d49d860c08c6ca142a08e\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
activity#com.google.android.play.core.common.PlayCoreDialogWrapperActivity
ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\512de977da2a1ddbca08eb2f7061b9db\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
	android:stateNotNeeded
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\512de977da2a1ddbca08eb2f7061b9db\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
	android:exported
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\512de977da2a1ddbca08eb2f7061b9db\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
	android:theme
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\512de977da2a1ddbca08eb2f7061b9db\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
	android:name
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\512de977da2a1ddbca08eb2f7061b9db\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
