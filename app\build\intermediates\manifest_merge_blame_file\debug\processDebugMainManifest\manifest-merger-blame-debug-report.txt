1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.uhabits_99"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="36" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:25:5-67
11-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:25:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:26:5-79
12-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:26:22-76
13    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
13-->[com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35088881d9fa2f6252e2bf289aa77253\transformed\recaptcha-18.5.1\AndroidManifest.xml:9:5-98
13-->[com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35088881d9fa2f6252e2bf289aa77253\transformed\recaptcha-18.5.1\AndroidManifest.xml:9:22-95
14
15    <permission
15-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0062a5e1d214efb62fafece10c34c46\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
16        android:name="com.example.uhabits_99.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
16-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0062a5e1d214efb62fafece10c34c46\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
17        android:protectionLevel="signature" />
17-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0062a5e1d214efb62fafece10c34c46\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
18
19    <uses-permission android:name="com.example.uhabits_99.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
19-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0062a5e1d214efb62fafece10c34c46\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
19-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0062a5e1d214efb62fafece10c34c46\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
20
21    <application
21-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:5:5-26:19
22        android:name="com.example.habits9.HabitsApplication"
22-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:6:9-61
23        android:allowBackup="true"
23-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:7:9-35
24        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
24-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0062a5e1d214efb62fafece10c34c46\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
25        android:dataExtractionRules="@xml/data_extraction_rules"
25-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:8:9-65
26        android:debuggable="true"
27        android:extractNativeLibs="false"
28        android:fullBackupContent="@xml/backup_rules"
28-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:9:9-54
29        android:icon="@mipmap/ic_launcher"
29-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:10:9-43
30        android:label="@string/app_name"
30-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:11:9-41
31        android:roundIcon="@mipmap/ic_launcher_round"
31-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:12:9-54
32        android:supportsRtl="true"
32-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:13:9-35
33        android:testOnly="true"
34        android:theme="@style/Theme.UHabits_99" >
34-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:14:9-48
35        <activity
35-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:15:9-25:20
36            android:name="com.example.uhabits_99.MainActivity"
36-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:16:13-41
37            android:exported="true"
37-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:17:13-36
38            android:label="@string/app_name"
38-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:18:13-45
39            android:theme="@style/Theme.UHabits_99" >
39-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:19:13-52
40            <intent-filter>
40-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:20:13-24:29
41                <action android:name="android.intent.action.MAIN" />
41-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:21:17-69
41-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:21:25-66
42
43                <category android:name="android.intent.category.LAUNCHER" />
43-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:23:17-77
43-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:23:27-74
44            </intent-filter>
45        </activity>
46
47        <service
47-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1b164dcef77965ed0bfc5203894ab9f\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:8:9-14:19
48            android:name="com.google.firebase.components.ComponentDiscoveryService"
48-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1b164dcef77965ed0bfc5203894ab9f\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:9:13-84
49            android:directBootAware="true"
49-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
50            android:exported="false" >
50-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1b164dcef77965ed0bfc5203894ab9f\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:10:13-37
51            <meta-data
51-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1b164dcef77965ed0bfc5203894ab9f\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:11:13-13:85
52                android:name="com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar"
52-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1b164dcef77965ed0bfc5203894ab9f\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:12:17-119
53                android:value="com.google.firebase.components.ComponentRegistrar" />
53-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1b164dcef77965ed0bfc5203894ab9f\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:13:17-82
54            <meta-data
54-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:69:13-71:85
55                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
55-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:70:17-109
56                android:value="com.google.firebase.components.ComponentRegistrar" />
56-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:71:17-82
57            <meta-data
57-->[com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\196d76f4a5dd55b436535b048bf59be0\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:12:13-14:85
58                android:name="com.google.firebase.components:com.google.firebase.firestore.ktx.FirebaseFirestoreLegacyRegistrar"
58-->[com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\196d76f4a5dd55b436535b048bf59be0\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:13:17-129
59                android:value="com.google.firebase.components.ComponentRegistrar" />
59-->[com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\196d76f4a5dd55b436535b048bf59be0\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:14:17-82
60            <meta-data
60-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847f490becd90c69cf2a6765d8a1684e\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:17:13-19:85
61                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
61-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847f490becd90c69cf2a6765d8a1684e\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:18:17-122
62                android:value="com.google.firebase.components.ComponentRegistrar" />
62-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847f490becd90c69cf2a6765d8a1684e\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:19:17-82
63            <meta-data
63-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847f490becd90c69cf2a6765d8a1684e\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:20:13-22:85
64                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
64-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847f490becd90c69cf2a6765d8a1684e\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:21:17-111
65                android:value="com.google.firebase.components.ComponentRegistrar" />
65-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847f490becd90c69cf2a6765d8a1684e\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:22:17-82
66            <meta-data
66-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\974ec625152fe404856b82ebcfbe2275\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
67                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
67-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\974ec625152fe404856b82ebcfbe2275\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
68                android:value="com.google.firebase.components.ComponentRegistrar" />
68-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\974ec625152fe404856b82ebcfbe2275\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
69            <meta-data
69-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
70                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
70-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
71                android:value="com.google.firebase.components.ComponentRegistrar" />
71-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
72        </service>
73
74        <activity
74-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:29:9-46:20
75            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
75-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:30:13-80
76            android:excludeFromRecents="true"
76-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:31:13-46
77            android:exported="true"
77-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:32:13-36
78            android:launchMode="singleTask"
78-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:33:13-44
79            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
79-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:34:13-72
80            <intent-filter>
80-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:35:13-45:29
81                <action android:name="android.intent.action.VIEW" />
81-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:17-69
81-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:25-66
82
83                <category android:name="android.intent.category.DEFAULT" />
83-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:17-76
83-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:27-73
84                <category android:name="android.intent.category.BROWSABLE" />
84-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:17-78
84-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:27-75
85
86                <data
86-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
87                    android:host="firebase.auth"
87-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
88                    android:path="/"
88-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:43:21-37
89                    android:scheme="genericidp" />
89-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
90            </intent-filter>
91        </activity>
92        <activity
92-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:47:9-64:20
93            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
93-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:48:13-79
94            android:excludeFromRecents="true"
94-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:49:13-46
95            android:exported="true"
95-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:50:13-36
96            android:launchMode="singleTask"
96-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:51:13-44
97            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
97-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:52:13-72
98            <intent-filter>
98-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:53:13-63:29
99                <action android:name="android.intent.action.VIEW" />
99-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:17-69
99-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:25-66
100
101                <category android:name="android.intent.category.DEFAULT" />
101-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:17-76
101-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:27-73
102                <category android:name="android.intent.category.BROWSABLE" />
102-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:17-78
102-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:27-75
103
104                <data
104-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
105                    android:host="firebase.auth"
105-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
106                    android:path="/"
106-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:43:21-37
107                    android:scheme="recaptcha" />
107-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
108            </intent-filter>
109        </activity>
110
111        <service
111-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
112            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
112-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
113            android:enabled="true"
113-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
114            android:exported="false" >
114-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
115            <meta-data
115-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
116                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
116-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
117                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
117-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
118        </service>
119
120        <activity
120-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
121            android:name="androidx.credentials.playservices.HiddenActivity"
121-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
122            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
122-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
123            android:enabled="true"
123-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
124            android:exported="false"
124-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
125            android:fitsSystemWindows="true"
125-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
126            android:theme="@style/Theme.Hidden" >
126-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
127        </activity>
128        <activity
128-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
129            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
129-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
130            android:excludeFromRecents="true"
130-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
131            android:exported="false"
131-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
132            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
132-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
133        <!--
134            Service handling Google Sign-In user revocation. For apps that do not integrate with
135            Google Sign-In, this service will never be started.
136        -->
137        <service
137-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
138            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
138-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
139            android:exported="true"
139-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
140            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
140-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
141            android:visibleToInstantApps="true" />
141-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
142
143        <activity
143-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6806dc609573cce0b061254c5042724b\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
144            android:name="com.google.android.gms.common.api.GoogleApiActivity"
144-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6806dc609573cce0b061254c5042724b\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
145            android:exported="false"
145-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6806dc609573cce0b061254c5042724b\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
146            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
146-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6806dc609573cce0b061254c5042724b\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
147
148        <provider
148-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
149            android:name="com.google.firebase.provider.FirebaseInitProvider"
149-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
150            android:authorities="com.example.uhabits_99.firebaseinitprovider"
150-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
151            android:directBootAware="true"
151-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
152            android:exported="false"
152-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
153            android:initOrder="100" />
153-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
154
155        <activity
155-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\15984bfa3c816c6e34dc7fd6da429334\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
156            android:name="androidx.compose.ui.tooling.PreviewActivity"
156-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\15984bfa3c816c6e34dc7fd6da429334\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
157            android:exported="true" />
157-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\15984bfa3c816c6e34dc7fd6da429334\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
158        <activity
158-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\d77bbb92f3326cfe733c63df1e46ad56\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:23:9-25:39
159            android:name="androidx.activity.ComponentActivity"
159-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\d77bbb92f3326cfe733c63df1e46ad56\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:24:13-63
160            android:exported="true" />
160-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\d77bbb92f3326cfe733c63df1e46ad56\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:25:13-36
161
162        <provider
162-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba89c1e9bda300913f5635e34e46d318\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
163            android:name="androidx.startup.InitializationProvider"
163-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba89c1e9bda300913f5635e34e46d318\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
164            android:authorities="com.example.uhabits_99.androidx-startup"
164-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba89c1e9bda300913f5635e34e46d318\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
165            android:exported="false" >
165-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba89c1e9bda300913f5635e34e46d318\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
166            <meta-data
166-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba89c1e9bda300913f5635e34e46d318\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
167                android:name="androidx.emoji2.text.EmojiCompatInitializer"
167-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba89c1e9bda300913f5635e34e46d318\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
168                android:value="androidx.startup" />
168-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba89c1e9bda300913f5635e34e46d318\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
169            <meta-data
169-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6ef72d2134d492a4ad92cbd50b61265\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
170                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
170-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6ef72d2134d492a4ad92cbd50b61265\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
171                android:value="androidx.startup" />
171-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6ef72d2134d492a4ad92cbd50b61265\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
172            <meta-data
172-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
173                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
173-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
174                android:value="androidx.startup" />
174-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
175        </provider>
176
177        <meta-data
177-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4106571b134d49d860c08c6ca142a08e\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
178            android:name="com.google.android.gms.version"
178-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4106571b134d49d860c08c6ca142a08e\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
179            android:value="@integer/google_play_services_version" />
179-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4106571b134d49d860c08c6ca142a08e\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
180
181        <receiver
181-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
182            android:name="androidx.profileinstaller.ProfileInstallReceiver"
182-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
183            android:directBootAware="false"
183-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
184            android:enabled="true"
184-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
185            android:exported="true"
185-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
186            android:permission="android.permission.DUMP" >
186-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
187            <intent-filter>
187-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
188                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
188-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
188-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
189            </intent-filter>
190            <intent-filter>
190-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
191                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
191-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
191-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
192            </intent-filter>
193            <intent-filter>
193-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
194                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
194-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
194-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
195            </intent-filter>
196            <intent-filter>
196-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
197                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
197-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
197-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
198            </intent-filter>
199        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
200        <activity
200-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\512de977da2a1ddbca08eb2f7061b9db\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
201            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
201-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\512de977da2a1ddbca08eb2f7061b9db\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
202            android:exported="false"
202-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\512de977da2a1ddbca08eb2f7061b9db\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
203            android:stateNotNeeded="true"
203-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\512de977da2a1ddbca08eb2f7061b9db\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
204            android:theme="@style/Theme.PlayCore.Transparent" />
204-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\512de977da2a1ddbca08eb2f7061b9db\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
205    </application>
206
207</manifest>
