package com.example.habits9.ui.createhabit;

import com.example.habits9.data.HabitRepository;
import com.example.habits9.data.HabitSectionRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CreateHabitViewModel_Factory implements Factory<CreateHabitViewModel> {
  private final Provider<HabitSectionRepository> habitSectionRepositoryProvider;

  private final Provider<HabitRepository> habitRepositoryProvider;

  public CreateHabitViewModel_Factory(
      Provider<HabitSectionRepository> habitSectionRepositoryProvider,
      Provider<HabitRepository> habitRepositoryProvider) {
    this.habitSectionRepositoryProvider = habitSectionRepositoryProvider;
    this.habitRepositoryProvider = habitRepositoryProvider;
  }

  @Override
  public CreateHabitViewModel get() {
    return newInstance(habitSectionRepositoryProvider.get(), habitRepositoryProvider.get());
  }

  public static CreateHabitViewModel_Factory create(
      Provider<HabitSectionRepository> habitSectionRepositoryProvider,
      Provider<HabitRepository> habitRepositoryProvider) {
    return new CreateHabitViewModel_Factory(habitSectionRepositoryProvider, habitRepositoryProvider);
  }

  public static CreateHabitViewModel newInstance(HabitSectionRepository habitSectionRepository,
      HabitRepository habitRepository) {
    return new CreateHabitViewModel(habitSectionRepository, habitRepository);
  }
}
