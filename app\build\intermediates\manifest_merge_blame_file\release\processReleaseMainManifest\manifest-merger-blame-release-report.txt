1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.uhabits_99"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="36" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:25:5-67
11-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:25:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:26:5-79
12-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:26:22-76
13    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
13-->[com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35088881d9fa2f6252e2bf289aa77253\transformed\recaptcha-18.5.1\AndroidManifest.xml:9:5-98
13-->[com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\35088881d9fa2f6252e2bf289aa77253\transformed\recaptcha-18.5.1\AndroidManifest.xml:9:22-95
14
15    <permission
15-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0062a5e1d214efb62fafece10c34c46\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
16        android:name="com.example.uhabits_99.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
16-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0062a5e1d214efb62fafece10c34c46\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
17        android:protectionLevel="signature" />
17-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0062a5e1d214efb62fafece10c34c46\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
18
19    <uses-permission android:name="com.example.uhabits_99.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
19-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0062a5e1d214efb62fafece10c34c46\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
19-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0062a5e1d214efb62fafece10c34c46\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
20
21    <application
21-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:5:5-26:19
22        android:name="com.example.habits9.HabitsApplication"
22-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:6:9-61
23        android:allowBackup="true"
23-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:7:9-35
24        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
24-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0062a5e1d214efb62fafece10c34c46\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
25        android:dataExtractionRules="@xml/data_extraction_rules"
25-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:8:9-65
26        android:extractNativeLibs="false"
27        android:fullBackupContent="@xml/backup_rules"
27-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:9:9-54
28        android:icon="@mipmap/ic_launcher"
28-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:10:9-43
29        android:label="@string/app_name"
29-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:11:9-41
30        android:roundIcon="@mipmap/ic_launcher_round"
30-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:12:9-54
31        android:supportsRtl="true"
31-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:13:9-35
32        android:theme="@style/Theme.UHabits_99" >
32-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:14:9-48
33        <activity
33-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:15:9-25:20
34            android:name="com.example.uhabits_99.MainActivity"
34-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:16:13-41
35            android:exported="true"
35-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:17:13-36
36            android:label="@string/app_name"
36-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:18:13-45
37            android:theme="@style/Theme.UHabits_99" >
37-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:19:13-52
38            <intent-filter>
38-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:20:13-24:29
39                <action android:name="android.intent.action.MAIN" />
39-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:21:17-69
39-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:21:25-66
40
41                <category android:name="android.intent.category.LAUNCHER" />
41-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:23:17-77
41-->E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:23:27-74
42            </intent-filter>
43        </activity>
44
45        <service
45-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1b164dcef77965ed0bfc5203894ab9f\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:8:9-14:19
46            android:name="com.google.firebase.components.ComponentDiscoveryService"
46-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1b164dcef77965ed0bfc5203894ab9f\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:9:13-84
47            android:directBootAware="true"
47-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
48            android:exported="false" >
48-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1b164dcef77965ed0bfc5203894ab9f\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:10:13-37
49            <meta-data
49-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1b164dcef77965ed0bfc5203894ab9f\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:11:13-13:85
50                android:name="com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar"
50-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1b164dcef77965ed0bfc5203894ab9f\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:12:17-119
51                android:value="com.google.firebase.components.ComponentRegistrar" />
51-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1b164dcef77965ed0bfc5203894ab9f\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:13:17-82
52            <meta-data
52-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:69:13-71:85
53                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
53-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:70:17-109
54                android:value="com.google.firebase.components.ComponentRegistrar" />
54-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:71:17-82
55            <meta-data
55-->[com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\196d76f4a5dd55b436535b048bf59be0\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:12:13-14:85
56                android:name="com.google.firebase.components:com.google.firebase.firestore.ktx.FirebaseFirestoreLegacyRegistrar"
56-->[com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\196d76f4a5dd55b436535b048bf59be0\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:13:17-129
57                android:value="com.google.firebase.components.ComponentRegistrar" />
57-->[com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\196d76f4a5dd55b436535b048bf59be0\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:14:17-82
58            <meta-data
58-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847f490becd90c69cf2a6765d8a1684e\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:17:13-19:85
59                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
59-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847f490becd90c69cf2a6765d8a1684e\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:18:17-122
60                android:value="com.google.firebase.components.ComponentRegistrar" />
60-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847f490becd90c69cf2a6765d8a1684e\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:19:17-82
61            <meta-data
61-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847f490becd90c69cf2a6765d8a1684e\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:20:13-22:85
62                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
62-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847f490becd90c69cf2a6765d8a1684e\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:21:17-111
63                android:value="com.google.firebase.components.ComponentRegistrar" />
63-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\847f490becd90c69cf2a6765d8a1684e\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:22:17-82
64            <meta-data
64-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\974ec625152fe404856b82ebcfbe2275\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
65                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
65-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\974ec625152fe404856b82ebcfbe2275\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
66                android:value="com.google.firebase.components.ComponentRegistrar" />
66-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\974ec625152fe404856b82ebcfbe2275\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
67            <meta-data
67-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
68                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
68-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
69                android:value="com.google.firebase.components.ComponentRegistrar" />
69-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
70        </service>
71
72        <activity
72-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:29:9-46:20
73            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
73-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:30:13-80
74            android:excludeFromRecents="true"
74-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:31:13-46
75            android:exported="true"
75-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:32:13-36
76            android:launchMode="singleTask"
76-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:33:13-44
77            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
77-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:34:13-72
78            <intent-filter>
78-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:35:13-45:29
79                <action android:name="android.intent.action.VIEW" />
79-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:17-69
79-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:25-66
80
81                <category android:name="android.intent.category.DEFAULT" />
81-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:17-76
81-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:27-73
82                <category android:name="android.intent.category.BROWSABLE" />
82-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:17-78
82-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:27-75
83
84                <data
84-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
85                    android:host="firebase.auth"
85-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
86                    android:path="/"
86-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:43:21-37
87                    android:scheme="genericidp" />
87-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
88            </intent-filter>
89        </activity>
90        <activity
90-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:47:9-64:20
91            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
91-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:48:13-79
92            android:excludeFromRecents="true"
92-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:49:13-46
93            android:exported="true"
93-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:50:13-36
94            android:launchMode="singleTask"
94-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:51:13-44
95            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
95-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:52:13-72
96            <intent-filter>
96-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:53:13-63:29
97                <action android:name="android.intent.action.VIEW" />
97-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:17-69
97-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:25-66
98
99                <category android:name="android.intent.category.DEFAULT" />
99-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:17-76
99-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:27-73
100                <category android:name="android.intent.category.BROWSABLE" />
100-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:17-78
100-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:27-75
101
102                <data
102-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
103                    android:host="firebase.auth"
103-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
104                    android:path="/"
104-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:43:21-37
105                    android:scheme="recaptcha" />
105-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c27c21065048b283cee9efed8bf7aaa1\transformed\firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
106            </intent-filter>
107        </activity>
108
109        <service
109-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
110            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
110-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
111            android:enabled="true"
111-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
112            android:exported="false" >
112-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
113            <meta-data
113-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
114                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
114-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
115                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
115-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
116        </service>
117
118        <activity
118-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
119            android:name="androidx.credentials.playservices.HiddenActivity"
119-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
120            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
120-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
121            android:enabled="true"
121-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
122            android:exported="false"
122-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
123            android:fitsSystemWindows="true"
123-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
124            android:theme="@style/Theme.Hidden" >
124-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e68568aef666c6b33debb00dcf099e5\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
125        </activity>
126        <activity
126-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
127            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
127-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
128            android:excludeFromRecents="true"
128-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
129            android:exported="false"
129-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
130            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
130-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
131        <!--
132            Service handling Google Sign-In user revocation. For apps that do not integrate with
133            Google Sign-In, this service will never be started.
134        -->
135        <service
135-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
136            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
136-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
137            android:exported="true"
137-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
138            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
138-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
139            android:visibleToInstantApps="true" />
139-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3cf27091625c70315b84b2896f5df28\transformed\play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
140
141        <activity
141-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6806dc609573cce0b061254c5042724b\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
142            android:name="com.google.android.gms.common.api.GoogleApiActivity"
142-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6806dc609573cce0b061254c5042724b\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
143            android:exported="false"
143-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6806dc609573cce0b061254c5042724b\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
144            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
144-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6806dc609573cce0b061254c5042724b\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
145
146        <provider
146-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
147            android:name="com.google.firebase.provider.FirebaseInitProvider"
147-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
148            android:authorities="com.example.uhabits_99.firebaseinitprovider"
148-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
149            android:directBootAware="true"
149-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
150            android:exported="false"
150-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
151            android:initOrder="100" />
151-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b2ba2190bc9c73781077a8484d322a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
152        <provider
152-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba89c1e9bda300913f5635e34e46d318\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
153            android:name="androidx.startup.InitializationProvider"
153-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba89c1e9bda300913f5635e34e46d318\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
154            android:authorities="com.example.uhabits_99.androidx-startup"
154-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba89c1e9bda300913f5635e34e46d318\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
155            android:exported="false" >
155-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba89c1e9bda300913f5635e34e46d318\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
156            <meta-data
156-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba89c1e9bda300913f5635e34e46d318\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
157                android:name="androidx.emoji2.text.EmojiCompatInitializer"
157-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba89c1e9bda300913f5635e34e46d318\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
158                android:value="androidx.startup" />
158-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba89c1e9bda300913f5635e34e46d318\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
159            <meta-data
159-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6ef72d2134d492a4ad92cbd50b61265\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
160                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
160-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6ef72d2134d492a4ad92cbd50b61265\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
161                android:value="androidx.startup" />
161-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6ef72d2134d492a4ad92cbd50b61265\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
162            <meta-data
162-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
163                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
163-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
164                android:value="androidx.startup" />
164-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
165        </provider>
166
167        <meta-data
167-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4106571b134d49d860c08c6ca142a08e\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
168            android:name="com.google.android.gms.version"
168-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4106571b134d49d860c08c6ca142a08e\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
169            android:value="@integer/google_play_services_version" />
169-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4106571b134d49d860c08c6ca142a08e\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
170
171        <receiver
171-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
172            android:name="androidx.profileinstaller.ProfileInstallReceiver"
172-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
173            android:directBootAware="false"
173-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
174            android:enabled="true"
174-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
175            android:exported="true"
175-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
176            android:permission="android.permission.DUMP" >
176-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
177            <intent-filter>
177-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
178                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
178-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
178-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
179            </intent-filter>
180            <intent-filter>
180-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
181                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
181-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
181-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
182            </intent-filter>
183            <intent-filter>
183-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
184                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
184-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
184-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
185            </intent-filter>
186            <intent-filter>
186-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
187                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
187-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
187-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2369a50817f7e82010f69c11bbb3a93b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
188            </intent-filter>
189        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
190        <activity
190-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\512de977da2a1ddbca08eb2f7061b9db\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
191            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
191-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\512de977da2a1ddbca08eb2f7061b9db\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
192            android:exported="false"
192-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\512de977da2a1ddbca08eb2f7061b9db\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
193            android:stateNotNeeded="true"
193-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\512de977da2a1ddbca08eb2f7061b9db\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
194            android:theme="@style/Theme.PlayCore.Transparent" />
194-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\512de977da2a1ddbca08eb2f7061b9db\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
195    </application>
196
197</manifest>
